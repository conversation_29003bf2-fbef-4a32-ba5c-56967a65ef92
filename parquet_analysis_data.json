[{"file_name": "communities.parquet", "file_path": "output\\communities.parquet", "file_size_mb": 0.01, "row_count": 3, "column_count": 12, "columns": {"id": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["11aa290b-c85c-43d5-9093-5e7285500287", "27510ac0-537b-468a-8bcb-83f105e249d1", "ade84b4c-e6c9-44af-961b-235ab4e7eda0"], "unique_count": 3}, "human_readable_id": {"data_type": "int64", "null_count": "0", "null_percentage": 0.0, "sample_values": ["0", "1", "2"], "unique_count": 3, "statistics": {"min": "0", "max": "2", "mean": 1.0, "std": 1.0}}, "community": {"data_type": "int64", "null_count": "0", "null_percentage": 0.0, "sample_values": ["0", "1", "2"], "unique_count": 3, "statistics": {"min": "0", "max": "2", "mean": 1.0, "std": 1.0}}, "level": {"data_type": "int64", "null_count": "0", "null_percentage": 0.0, "sample_values": ["0", "0", "0"], "unique_count": 1, "statistics": {"min": "0", "max": "0", "mean": 0.0, "std": 0.0}}, "parent": {"data_type": "int32", "null_count": "0", "null_percentage": 0.0, "sample_values": ["-1", "-1", "-1"], "unique_count": 1, "statistics": {"min": "-1", "max": "-1", "mean": -1.0, "std": 0.0}}, "children": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["[Array with 0 items]", "[Array with 0 items]", "[Array with 0 items]"], "unique_count": "Cannot calculate (complex data type)"}, "title": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["Community 0", "Community 1", "Community 2"], "unique_count": 3}, "entity_ids": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["[Array with 7 items]", "[Array with 4 items]", "[Array with 2 items]"], "unique_count": "Cannot calculate (complex data type)"}, "relationship_ids": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["[Array with 14 items]", "[Array with 6 items]", "[Array with 1 items]"], "unique_count": "Cannot calculate (complex data type)"}, "text_unit_ids": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["[Array with 15 items]", "[Array with 14 items]", "[Array with 1 items]"], "unique_count": "Cannot calculate (complex data type)"}, "period": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["2025-06-27", "2025-06-27", "2025-06-27"], "unique_count": 1}, "size": {"data_type": "int64", "null_count": "0", "null_percentage": 0.0, "sample_values": ["7", "4", "2"], "unique_count": 3, "statistics": {"min": "2", "max": "7", "mean": 4.3333, "std": 2.5166}}}}, {"file_name": "community_reports.parquet", "file_path": "output\\community_reports.parquet", "file_size_mb": 0.05, "row_count": 3, "column_count": 15, "columns": {"id": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["28775e6d80934d5fad5c09778f623ebc", "210268a39b514384b3db1a8ce8d74983", "d949af32d68349158e2f5a4d2da72ffd"], "unique_count": 3}, "human_readable_id": {"data_type": "int32", "null_count": "0", "null_percentage": 0.0, "sample_values": ["0", "1", "2"], "unique_count": 3, "statistics": {"min": "0", "max": "2", "mean": 1.0, "std": 1.0}}, "community": {"data_type": "int32", "null_count": "0", "null_percentage": 0.0, "sample_values": ["0", "1", "2"], "unique_count": 3, "statistics": {"min": "0", "max": "2", "mean": 1.0, "std": 1.0}}, "level": {"data_type": "int64", "null_count": "0", "null_percentage": 0.0, "sample_values": ["0", "0", "0"], "unique_count": 1, "statistics": {"min": "0", "max": "0", "mean": 0.0, "std": 0.0}}, "parent": {"data_type": "int32", "null_count": "0", "null_percentage": 0.0, "sample_values": ["-1", "-1", "-1"], "unique_count": 1, "statistics": {"min": "-1", "max": "-1", "mean": -1.0, "std": 0.0}}, "children": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["[Array with 0 items]", "[Array with 0 items]", "[Array with 0 items]"], "unique_count": "Cannot calculate (complex data type)"}, "title": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["Python Function Structure: 函数, 形参, 返回值, 列表, WHILE循环", "函数实参类型与传递规则社区", "参数列表与参数列表行结构社区"], "unique_count": 3}, "summary": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["This community centers on the core components of Python function design and usage, including functio", "本社区围绕函数调用中的实参类型及其传递规则展开，涵盖了实参、位置实参、关键字实参以及位置实参的顺序等核心概念。各实体之间通过‘IS_A’、‘HAS_PROPERTY’和‘RELATED_TO’等关系紧", "本社区围绕“参数列表”及其组成部分“参数列表行”展开，描述了函数定义中参数列表的结构化组织方式。参数列表作为核心实体，包含多个参数列表行，而每个参数列表行则作为参数列表的组成部分。社区结构清晰，实体之"], "unique_count": 3}, "full_content": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["# Python Function Structure: 函数, 形参, 返回值, 列表, WHILE循环\n\nThis community centers on the core components", "# 函数实参类型与传递规则社区\n\n本社区围绕函数调用中的实参类型及其传递规则展开，涵盖了实参、位置实参、关键字实参以及位置实参的顺序等核心概念。各实体之间通过‘IS_A’、‘HAS_PROPERTY’", "# 参数列表与参数列表行结构社区\n\n本社区围绕“参数列表”及其组成部分“参数列表行”展开，描述了函数定义中参数列表的结构化组织方式。参数列表作为核心实体，包含多个参数列表行，而每个参数列表行则作为参数"], "unique_count": 3}, "rank": {"data_type": "float64", "null_count": "0", "null_percentage": 0.0, "sample_values": ["8.5", "6.5", "2.0"], "unique_count": 3, "statistics": {"min": 2.0, "max": 8.5, "mean": 5.6667, "std": 3.3292}}, "rating_explanation": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["The impact severity rating is high due to the foundational role these entities play in Python progra", "该社区对编程实践具有较高的重要性，直接影响代码的正确性、可读性和维护性，但其影响范围主要局限于编程和软件开发领域。", "该社区的影响程度较低，主要涉及编程结构的基础组成，对更广泛系统或社会层面影响有限。"], "unique_count": 3}, "findings": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["[Array with 8 items]", "[Array with 7 items]", "[Array with 5 items]"], "unique_count": "Cannot calculate (complex data type)"}, "full_content_json": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["{\n    \"title\": \"Python Function Structure: 函数, 形参, 返回值, 列表, WHILE循环\",\n    \"summary\": \"This community", "{\n    \"title\": \"函数实参类型与传递规则社区\",\n    \"summary\": \"本社区围绕函数调用中的实参类型及其传递规则展开，涵盖了实参、位置实参、关键字实参以及位置实参的顺序等核心", "{\n    \"title\": \"参数列表与参数列表行结构社区\",\n    \"summary\": \"本社区围绕“参数列表”及其组成部分“参数列表行”展开，描述了函数定义中参数列表的结构化组织方式。参数列"], "unique_count": 3}, "period": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["2025-06-27", "2025-06-27", "2025-06-27"], "unique_count": 1}, "size": {"data_type": "int64", "null_count": "0", "null_percentage": 0.0, "sample_values": ["7", "4", "2"], "unique_count": 3, "statistics": {"min": "2", "max": "7", "mean": 4.3333, "std": 2.5166}}}}, {"file_name": "documents.parquet", "file_path": "output\\documents.parquet", "file_size_mb": 0.03, "row_count": 1, "column_count": 7, "columns": {"id": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["3ebcb132392d910d998ca11f5aebae5ff2817f72c7d4b350c56da6ca3159f38a27d23b8973b02d19f1927ad4b49b5f5e0227"], "unique_count": 1}, "human_readable_id": {"data_type": "int64", "null_count": "0", "null_percentage": 0.0, "sample_values": ["1"], "unique_count": 1, "statistics": {"min": "1", "max": "1", "mean": 1.0, "std": NaN}}, "title": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["第八章.txt"], "unique_count": 1}, "text": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["## 第 8 章　函数\n\n在本章中，你将学习编写 函数 （ function ）。函数是带名字的代码 块，用于完成具体的工作。要执行函数定义的特定任务，可 调用 （ call ）该函数。当需要在程序中"], "unique_count": 1}, "text_unit_ids": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["[Array with 16 items]"], "unique_count": "Cannot calculate (complex data type)"}, "creation_date": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["2025-06-27 11:25:56 +0800"], "unique_count": 1}, "metadata": {"data_type": "object", "null_count": "1", "null_percentage": 100.0, "sample_values": [], "unique_count": 0}}}, {"file_name": "entities.parquet", "file_path": "output\\entities.parquet", "file_size_mb": 0.01, "row_count": 13, "column_count": 10, "columns": {"id": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["6b29a49e-1c0f-4178-aa5e-687f8727dd09", "1e827128-6f38-4717-a602-9477dce3ec71", "c3c08b0a-4a73-4789-b986-d16bc9297d4f", "79243777-bcf8-498b-b60e-0e6f033ebeb5", "e6aa2225-efd3-4926-843c-da04c46e307f"], "unique_count": 13}, "human_readable_id": {"data_type": "int64", "null_count": "0", "null_percentage": 0.0, "sample_values": ["0", "1", "2", "3", "4"], "unique_count": 13, "statistics": {"min": "0", "max": "12", "mean": 6.0, "std": 3.8944}}, "title": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["函数", "形参", "实参", "返回值", "位置实参的顺序"], "unique_count": 13}, "type": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["函数", "形参", "实参", "返回值", "位置实参的顺序"], "unique_count": 10}, "description": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["函数是具有特定名称的代码块，用于封装和完成特定的操作或任务。通过定义函数，可以将相关的代码组织在一起，实现结构化编程和代码复用。函数通常包含形参（参数变量），用于在调用时接收外部传入的数据（实参），并", "“形参”是指在函数定义时，在括号内声明的变量名，用于接收调用函数时传递进来的实参值。形参决定了函数可以接受哪些输入，以及函数调用时需要提供哪些数据。它们在函数体内被使用，是函数完成任务所需的信息。例如", "“实参”是指在调用函数时传递给函数的实际值。每当函数被调用时，实参为函数的形参提供具体的数据，使函数能够根据这些输入执行相应的操作。实参的类型非常灵活，可以是数字、字符串、列表等各种数据类型。例如，在", "“返回值”是指函数在执行完毕后，通过return语句返回给调用者的数据或结果。返回值的主要作用是将函数内部处理的结果反馈或传递给主程序或调用者，以便后续使用。返回值可以是一个值，也可以是多个值，类型没", "“位置实参的顺序”是指在函数调用时，实参（即传入函数的参数）必须按照函数定义中形参（即函数声明时的参数）的排列顺序依次传递的规则。每个位置实参出现的先后顺序直接决定了它被赋值给哪个形参，因此顺序非常重"], "unique_count": 12}, "text_unit_ids": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["[Array with 15 items]", "[Array with 14 items]", "[Array with 14 items]", "[Array with 12 items]", "[Array with 14 items]"], "unique_count": "Cannot calculate (complex data type)"}, "frequency": {"data_type": "int64", "null_count": "0", "null_percentage": 0.0, "sample_values": ["15", "14", "14", "12", "14"], "unique_count": 7, "statistics": {"min": "1", "max": "15", "mean": 9.0769, "std": 5.7076}}, "degree": {"data_type": "int32", "null_count": "0", "null_percentage": 0.0, "sample_values": ["11", "8", "7", "4", "4"], "unique_count": 9, "statistics": {"min": "1", "max": "11", "mean": 4.9231, "std": 2.6914}}, "x": {"data_type": "int64", "null_count": "0", "null_percentage": 0.0, "sample_values": ["0", "0", "0", "0", "0"], "unique_count": 1, "statistics": {"min": "0", "max": "0", "mean": 0.0, "std": 0.0}}, "y": {"data_type": "int64", "null_count": "0", "null_percentage": 0.0, "sample_values": ["0", "0", "0", "0", "0"], "unique_count": 1, "statistics": {"min": "0", "max": "0", "mean": 0.0, "std": 0.0}}}}, {"file_name": "relationships.parquet", "file_path": "output\\relationships.parquet", "file_size_mb": 0.01, "row_count": 38, "column_count": 8, "columns": {"id": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["549443d5-e81c-47c0-91f7-c50169962df2", "0c94b16c-269b-41e7-b8ab-f86759f77060", "93af8808-735c-4960-88c6-2b4532df1593", "47325597-77f8-41cf-b183-d756385c7e1e", "6b46f2ac-6449-4645-a801-ef41f0e2a958"], "unique_count": 38}, "human_readable_id": {"data_type": "int64", "null_count": "0", "null_percentage": 0.0, "sample_values": ["0", "1", "2", "3", "4"], "unique_count": 38, "statistics": {"min": "0", "max": "37", "mean": 18.5, "std": 11.1131}}, "source": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["函数", "函数", "函数", "函数", "函数"], "unique_count": 12}, "target": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["形参", "实参", "返回值", "位置实参", "关键字实参"], "unique_count": 12}, "description": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["The entities \"函数\" (function) and \"形参\" (formal parameter) are closely related concepts in programming", "USES", "The entities \"函数\" (function) and \"返回值\" (return value) are closely related in the context of programm", "USES", "USES"], "unique_count": 19}, "weight": {"data_type": "float64", "null_count": "0", "null_percentage": 0.0, "sample_values": ["115.0", "100.0", "107.0", "29.0", "29.0"], "unique_count": 22, "statistics": {"min": 1.0, "max": 126.0, "mean": 34.3947, "std": 39.0007}}, "combined_degree": {"data_type": "int64", "null_count": "0", "null_percentage": 0.0, "sample_values": ["19", "18", "15", "16", "17"], "unique_count": 15, "statistics": {"min": "4", "max": "22", "mean": 12.4737, "std": 3.6668}}, "text_unit_ids": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["[Array with 15 items]", "[Array with 12 items]", "[Array with 14 items]", "[Array with 4 items]", "[Array with 4 items]"], "unique_count": "Cannot calculate (complex data type)"}}}, {"file_name": "relationships_with_types.parquet", "file_path": "output\\relationships_with_types.parquet", "file_size_mb": 0.01, "row_count": 38, "column_count": 10, "columns": {"id": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["549443d5-e81c-47c0-91f7-c50169962df2", "0c94b16c-269b-41e7-b8ab-f86759f77060", "93af8808-735c-4960-88c6-2b4532df1593", "47325597-77f8-41cf-b183-d756385c7e1e", "6b46f2ac-6449-4645-a801-ef41f0e2a958"], "unique_count": 38}, "human_readable_id": {"data_type": "int64", "null_count": "0", "null_percentage": 0.0, "sample_values": ["0", "1", "2", "3", "4"], "unique_count": 38, "statistics": {"min": "0", "max": "37", "mean": 18.5, "std": 11.1131}}, "source": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["函数", "函数", "函数", "函数", "函数"], "unique_count": 12}, "target": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["形参", "实参", "返回值", "位置实参", "关键字实参"], "unique_count": 12}, "description": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["The entities \"函数\" (function) and \"形参\" (formal parameter) are closely related concepts in programming", "USES", "The entities \"函数\" (function) and \"返回值\" (return value) are closely related in the context of programm", "USES", "USES"], "unique_count": 19}, "weight": {"data_type": "float64", "null_count": "0", "null_percentage": 0.0, "sample_values": ["115.0", "100.0", "107.0", "29.0", "29.0"], "unique_count": 22, "statistics": {"min": 1.0, "max": 126.0, "mean": 34.3947, "std": 39.0007}}, "combined_degree": {"data_type": "int64", "null_count": "0", "null_percentage": 0.0, "sample_values": ["19", "18", "15", "16", "17"], "unique_count": 15, "statistics": {"min": "4", "max": "22", "mean": 12.4737, "std": 3.6668}}, "text_unit_ids": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["[Array with 15 items]", "[Array with 12 items]", "[Array with 14 items]", "[Array with 4 items]", "[Array with 4 items]"], "unique_count": "Cannot calculate (complex data type)"}, "relationship_type": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["CONTAINS", "USES", "UNKNOWN", "USES", "USES"], "unique_count": 13}, "extracted_relationship_type": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["CONTAINS", "USES", "UNKNOWN", "USES", "USES"], "unique_count": 13}}}, {"file_name": "text_units.parquet", "file_path": "output\\text_units.parquet", "file_size_mb": 0.04, "row_count": 16, "column_count": 8, "columns": {"id": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["70b8d4c5fdbf97667f175052efa608b18b98a382ab28f4e4363665b7bc9191fe73b8ce7e44bdefda66deeb902a3809af5281", "3100d8839db4e3b2d8a1f94ac5db6b15ce07baa15c74d5fcb6ca5669a8dfd050be5f2731d04dc73dbc16edc78b55d8cc3d1f", "0a3f33c8880700596a56b57571128fb6ad4868ca4a5a4d9374a03979976aafeb92d27260741895998c32081599cb1262b189", "7205ab4a20bce3374940fe2032338f419d24562e3c198fe8636b8e335cc9984162d4a7024da5049e8349a8dcc8385b8d5140", "e448618d00a37a2b9317f692b258bb0db42beb6aa87443ac8d4f97b910a411d4dfd89fc170bf650a67dde3de84368e9b365e"], "unique_count": 16}, "human_readable_id": {"data_type": "int64", "null_count": "0", "null_percentage": 0.0, "sample_values": ["1", "2", "3", "4", "5"], "unique_count": 16, "statistics": {"min": "1", "max": "16", "mean": 8.5, "std": 4.761}}, "text": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["## 第 8 章　函数\n\n在本章中，你将学习编写 函数 （ function ）。函数是带名字的代码 块，用于完成具体的工作。要执行函数定义的特定任务，可 调用 （ call ）该函数。当需要在程序中", "供这种信息（人名）时，它将打印相应 的问候语。\n\n形参\n\n在 greet\\_user() 函数的定义中，变量 username 是-个 （ parameter ），即函数完成工作所需的信息。在代码 g", "名为 Willie 的 小狗的信息。至此，有-只名为 Harry 的仓鼠，还有-条名为 Willie 的小狗：\n\n```\nI have a hamster. My hamster's name is ", "pet() 来描述小狗时，就可以不提供该信息：\n\n```\nprint(f\"My {animal_type}'s name is {pet_name.title()}.\")\n```\n\n```\ndef d", "缺少必要的信息，并用 traceback 指出了这- 点：\n\n```\nTraceback (most recent call last): ❶ File \"pets.py\", line 6, in <"], "unique_count": 16}, "n_tokens": {"data_type": "int64", "null_count": "0", "null_percentage": 0.0, "sample_values": ["1200", "1200", "1200", "1200", "1200"], "unique_count": 2, "statistics": {"min": "366", "max": "1200", "mean": 1147.875, "std": 208.5}}, "document_ids": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["[Array with 1 items]", "[Array with 1 items]", "[Array with 1 items]", "[Array with 1 items]", "[Array with 1 items]"], "unique_count": "Cannot calculate (complex data type)"}, "entity_ids": {"data_type": "object", "null_count": "1", "null_percentage": 6.25, "sample_values": ["[Array with 8 items]", "[Array with 7 items]", "[Array with 6 items]", "[Array with 8 items]", "[Array with 8 items]"], "unique_count": "Cannot calculate (complex data type)"}, "relationship_ids": {"data_type": "object", "null_count": "1", "null_percentage": 6.25, "sample_values": ["[Array with 10 items]", "[Array with 10 items]", "[Array with 15 items]", "[Array with 13 items]", "[Array with 12 items]"], "unique_count": "Cannot calculate (complex data type)"}, "covariate_ids": {"data_type": "object", "null_count": "0", "null_percentage": 0.0, "sample_values": ["[Array with 0 items]", "[Array with 0 items]", "[Array with 0 items]", "[Array with 0 items]", "[Array with 0 items]"], "unique_count": "Cannot calculate (complex data type)"}}}]