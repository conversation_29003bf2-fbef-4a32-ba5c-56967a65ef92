[{"label": "pandas", "kind": 6, "isExtraImport": true, "importPath": "pandas", "description": "pandas", "detail": "pandas", "documentation": {}}, {"label": "json", "kind": 6, "isExtraImport": true, "importPath": "json", "description": "json", "detail": "json", "documentation": {}}, {"label": "re", "kind": 6, "isExtraImport": true, "importPath": "re", "description": "re", "detail": "re", "documentation": {}}, {"label": "Counter", "importPath": "collections", "description": "collections", "isExtraImport": true, "detail": "collections", "documentation": {}}, {"label": "analyze_relationships", "kind": 2, "importPath": "check_relationship_types", "description": "check_relationship_types", "peekOfCode": "def analyze_relationships():\n    \"\"\"分析关系数据中的关系类型\"\"\"\n    # 读取关系数据\n    df = pd.read_parquet('output/relationships.parquet')\n    print(\"🔍 分析关系类型信息\")\n    print(\"=\" * 50)\n    # 检查描述字段中的关系类型\n    print(\"\\n📋 关系描述分析:\")\n    print(f\"总关系数: {len(df)}\")\n    # 查看所有描述", "detail": "check_relationship_types", "documentation": {}}, {"label": "extract_relationship_type", "kind": 2, "importPath": "data_quality_report", "description": "data_quality_report", "peekOfCode": "def extract_relationship_type(description):\n    \"\"\"从描述中提取关系类型\"\"\"\n    # 预定义的关系类型列表\n    relation_types = [\n        'BELONGS_TO', 'IS_A', 'RELATED_TO', 'TEACHES', 'PREREQUISITE_OF',\n        'PART_OF', 'CAUSES', 'LOCATED_IN', 'MENTIONED_IN', 'WORKS_FOR',\n        'MANAGES', 'CONTAINS', 'OCCURS_BEFORE', 'LEADS_TO', 'COLLABORATES_WITH',\n        'OPPOSES', 'SIMILAR_TO', 'MENTIONS', 'CITES', 'AUTHORED_BY', 'PUBLISHED_IN',\n        'DERIVED_FROM', 'HAS_TOPIC', 'USES', 'EXTENDS', 'HAS_PROPERTY'\n    ]", "detail": "data_quality_report", "documentation": {}}, {"label": "generate_quality_report", "kind": 2, "importPath": "data_quality_report", "description": "data_quality_report", "peekOfCode": "def generate_quality_report():\n    \"\"\"生成数据质量报告\"\"\"\n    # 读取数据\n    df = pd.read_parquet('output/relationships.parquet')\n    print(\"📊 GraphRAG关系类型数据质量报告\")\n    print(\"=\" * 60)\n    # 基本统计\n    print(f\"\\n📈 基本统计:\")\n    print(f\"  总关系数: {len(df)}\")\n    print(f\"  唯一源节点: {df['source'].nunique()}\")", "detail": "data_quality_report", "documentation": {}}, {"label": "verify_relationship_fields", "kind": 2, "importPath": "verify_relationship_fields", "description": "verify_relationship_fields", "peekOfCode": "def verify_relationship_fields():\n    \"\"\"验证 relationships.parquet 中的字段结构\"\"\"\n    print(\"🔍 验证 GraphRAG relationships.parquet 文件结构\")\n    print(\"=\" * 60)\n    # 读取 relationships.parquet\n    relationships_df = pd.read_parquet('output/relationships.parquet')\n    print(f\"📊 Relationships.parquet 文件信息:\")\n    print(f\"  文件路径: output/relationships.parquet\")\n    print(f\"  行数: {len(relationships_df)}\")\n    print(f\"  列数: {len(relationships_df.columns)}\")", "detail": "verify_relationship_fields", "documentation": {}}, {"label": "compare_with_entities", "kind": 2, "importPath": "verify_relationship_fields", "description": "verify_relationship_fields", "peekOfCode": "def compare_with_entities():\n    \"\"\"比较 relationships.parquet 和 entities.parquet 的一致性\"\"\"\n    print(f\"\\n🔄 比较实体和关系数据的一致性:\")\n    entities_df = pd.read_parquet('output/entities.parquet')\n    relationships_df = pd.read_parquet('output/relationships.parquet')\n    # 从实体文件获取所有实体\n    entity_names = set(entities_df['title'].unique())\n    # 从关系文件获取所有节点\n    if 'source' in relationships_df.columns and 'target' in relationships_df.columns:\n        relationship_nodes = set(relationships_df['source'].unique()) | set(relationships_df['target'].unique())", "detail": "verify_relationship_fields", "documentation": {}}]