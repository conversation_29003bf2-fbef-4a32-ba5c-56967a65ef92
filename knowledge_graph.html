<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GraphRAG 知识图谱可视化</title>
    
    <!-- vis-network CSS -->
    <link href="https://unpkg.com/vis-network/standalone/umd/vis-network.min.css" rel="stylesheet" type="text/css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 300px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            overflow-y: auto;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
        }

        .main-content {
            flex: 1;
            position: relative;
        }

        #network-container {
            width: 100%;
            height: 100%;
            background: #ffffff;
            border-radius: 0 0 0 20px;
            box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 20px;
        }

        .header h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 5px;
        }

        .header p {
            color: #666;
            font-size: 14px;
        }

        .control-section {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            border: 1px solid #e0e0e0;
        }

        .control-section h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }

        .control-section h3::before {
            content: "🔧";
            margin-right: 8px;
        }

        .search-box {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            margin-bottom: 10px;
        }

        .filter-group {
            margin-bottom: 15px;
        }

        .filter-group label {
            display: block;
            color: #555;
            font-size: 14px;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .checkbox-group {
            max-height: 150px;
            overflow-y: auto;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            padding: 8px;
            background: #fafafa;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            padding: 3px;
        }

        .checkbox-item input[type="checkbox"] {
            margin-right: 8px;
        }

        .checkbox-item label {
            font-size: 12px;
            color: #666;
            margin: 0;
            cursor: pointer;
            flex: 1;
        }

        .color-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
            border: 1px solid #ccc;
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #ddd;
        }

        .btn-secondary:hover {
            background: #e9ecef;
        }

        .stats {
            background: rgba(102, 126, 234, 0.1);
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }

        .stats h4 {
            color: #333;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3px;
            font-size: 12px;
            color: #666;
        }

        .loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #667eea;
            font-size: 18px;
        }

        .loading::after {
            content: "";
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .legend {
            background: rgba(255, 255, 255, 0.9);
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
        }

        .legend h4 {
            color: #333;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            font-size: 11px;
            color: #666;
        }

        .legend-line {
            width: 20px;
            height: 2px;
            margin-right: 8px;
            border-radius: 1px;
        }

        .error-message {
            background: #ffebee;
            color: #c62828;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #c62828;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: 200px;
                order: 2;
            }
            
            .main-content {
                order: 1;
                height: calc(100vh - 200px);
            }
            
            #network-container {
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="header">
                <h1>🕸️ 知识图谱</h1>
                <p>GraphRAG 可视化分析</p>
            </div>

            <div class="control-section">
                <h3>搜索过滤</h3>
                <input type="text" id="search-input" class="search-box" placeholder="搜索节点名称...">
                <div class="button-group">
                    <button class="btn btn-primary" onclick="searchNodes()">搜索</button>
                    <button class="btn btn-secondary" onclick="clearSearch()">清除</button>
                </div>
            </div>

            <div class="control-section">
                <h3>关系类型过滤</h3>
                <div class="filter-group">
                    <div id="relationship-filters" class="checkbox-group">
                        <!-- 动态生成关系类型过滤器 -->
                    </div>
                </div>
                <div class="button-group">
                    <button class="btn btn-primary" onclick="selectAllRelationships()">全选</button>
                    <button class="btn btn-secondary" onclick="deselectAllRelationships()">全不选</button>
                </div>
            </div>

            <div class="control-section">
                <h3>布局控制</h3>
                <div class="button-group">
                    <button class="btn btn-primary" onclick="fitNetwork()">适应窗口</button>
                    <button class="btn btn-secondary" onclick="resetLayout()">重置布局</button>
                </div>
                <div class="button-group" style="margin-top: 10px;">
                    <button class="btn btn-primary" onclick="togglePhysics()">物理引擎</button>
                    <button class="btn btn-secondary" onclick="exportNetwork()">导出图片</button>
                </div>
            </div>

            <div class="stats">
                <h4>📊 图谱统计</h4>
                <div class="stat-item">
                    <span>节点数量:</span>
                    <span id="node-count">-</span>
                </div>
                <div class="stat-item">
                    <span>边数量:</span>
                    <span id="edge-count">-</span>
                </div>
                <div class="stat-item">
                    <span>关系类型:</span>
                    <span id="relationship-types">-</span>
                </div>
            </div>

            <div class="legend">
                <h4>🎨 关系图例</h4>
                <div id="relationship-legend">
                    <!-- 动态生成关系图例 -->
                </div>
            </div>
        </div>

        <div class="main-content">
            <div id="loading" class="loading">正在加载知识图谱...</div>
            <div id="network-container"></div>
        </div>
    </div>

    <!-- vis-network JavaScript -->
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    
    <script>
        // 全局变量
        let network = null;
        let nodes = null;
        let edges = null;
        let allNodes = [];
        let allEdges = [];
        let relationshipStyles = {};
        let physicsEnabled = true;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeVisualization();
        });

        async function initializeVisualization() {
            try {
                // 加载数据
                await loadData();
                
                // 创建网络
                createNetwork();
                
                // 设置事件监听
                setupEventListeners();
                
                // 隐藏加载提示
                document.getElementById('loading').style.display = 'none';
                
            } catch (error) {
                showError('加载失败: ' + error.message);
            }
        }

        async function loadData() {
            try {
                // 加载图数据
                const graphResponse = await fetch('graph_data.json');
                const graphData = await graphResponse.json();
                
                allNodes = graphData.nodes;
                allEdges = graphData.edges;
                
                // 加载样式配置
                const stylesResponse = await fetch('relationship_styles.json');
                relationshipStyles = await stylesResponse.json();
                
                // 应用样式到边数据
                applyStylesToEdges();
                
                // 更新统计信息
                updateStats();
                
                // 生成过滤器和图例
                generateRelationshipFilters();
                generateRelationshipLegend();
                
            } catch (error) {
                throw new Error('无法加载数据文件，请确保 graph_data.json 和 relationship_styles.json 文件存在');
            }
        }

        function applyStylesToEdges() {
            allEdges.forEach(edge => {
                const relationshipType = edge.label;
                const style = relationshipStyles.styles[relationshipType] || relationshipStyles.styles.UNKNOWN;
                
                // 应用样式
                Object.assign(edge, style);
            });
        }

        function createNetwork() {
            // 创建数据集
            nodes = new vis.DataSet(allNodes);
            edges = new vis.DataSet(allEdges);

            // 网络配置
            const options = {
                nodes: {
                    shape: 'dot',
                    size: 25,
                    font: {
                        size: 14,
                        color: '#000000'
                    },
                    borderWidth: 2,
                    shadow: true
                },
                edges: {
                    width: 2,
                    shadow: true,
                    smooth: {
                        enabled: true,
                        type: 'dynamic'
                    }
                },
                physics: {
                    enabled: true,
                    barnesHut: {
                        gravitationalConstant: -2000,
                        centralGravity: 0.3,
                        springLength: 95,
                        springConstant: 0.04,
                        damping: 0.09,
                        avoidOverlap: 0.1
                    },
                    maxVelocity: 50,
                    minVelocity: 0.1,
                    solver: 'barnesHut',
                    stabilization: {
                        enabled: true,
                        iterations: 1000,
                        updateInterval: 100
                    }
                },
                interaction: {
                    hover: true,
                    tooltipDelay: 300,
                    hideEdgesOnDrag: false,
                    hideNodesOnDrag: false
                },
                layout: {
                    improvedLayout: true,
                    clusterThreshold: 150
                }
            };

            // 创建网络
            const container = document.getElementById('network-container');
            network = new vis.Network(container, { nodes: nodes, edges: edges }, options);
        }

        function setupEventListeners() {
            // 网络事件
            network.on('click', function(params) {
                if (params.nodes.length > 0) {
                    const nodeId = params.nodes[0];
                    highlightConnectedNodes(nodeId);
                }
            });

            network.on('hoverNode', function(params) {
                // 可以添加悬停效果
            });

            // 搜索框事件
            document.getElementById('search-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchNodes();
                }
            });
        }

        function updateStats() {
            document.getElementById('node-count').textContent = allNodes.length;
            document.getElementById('edge-count').textContent = allEdges.length;
            
            const relationshipTypes = [...new Set(allEdges.map(edge => edge.label))];
            document.getElementById('relationship-types').textContent = relationshipTypes.length;
        }

        function generateRelationshipFilters() {
            const container = document.getElementById('relationship-filters');
            const relationshipTypes = [...new Set(allEdges.map(edge => edge.label))];
            
            container.innerHTML = '';
            
            relationshipTypes.forEach(type => {
                const style = relationshipStyles.styles[type] || relationshipStyles.styles.UNKNOWN;
                const color = style.color.color;
                
                const item = document.createElement('div');
                item.className = 'checkbox-item';
                item.innerHTML = `
                    <input type="checkbox" id="filter-${type}" checked onchange="filterRelationships()">
                    <div class="color-indicator" style="background-color: ${color}"></div>
                    <label for="filter-${type}">${type}</label>
                `;
                container.appendChild(item);
            });
        }

        function generateRelationshipLegend() {
            const container = document.getElementById('relationship-legend');
            const relationshipTypes = [...new Set(allEdges.map(edge => edge.label))];
            
            container.innerHTML = '';
            
            relationshipTypes.forEach(type => {
                const style = relationshipStyles.styles[type] || relationshipStyles.styles.UNKNOWN;
                const color = style.color.color;
                const isDashed = style.dashes && style.dashes.length > 0;
                
                const item = document.createElement('div');
                item.className = 'legend-item';
                item.innerHTML = `
                    <div class="legend-line" style="background-color: ${color}; ${isDashed ? 'background-image: linear-gradient(90deg, ' + color + ' 50%, transparent 50%); background-size: 8px 2px;' : ''}"></div>
                    <span>${type}</span>
                `;
                container.appendChild(item);
            });
        }

        // 控制函数
        function searchNodes() {
            const searchTerm = document.getElementById('search-input').value.toLowerCase();
            if (!searchTerm) {
                clearSearch();
                return;
            }

            const matchingNodes = allNodes.filter(node => 
                node.label.toLowerCase().includes(searchTerm)
            );

            if (matchingNodes.length > 0) {
                const nodeIds = matchingNodes.map(node => node.id);
                network.selectNodes(nodeIds);
                network.fit({ nodes: nodeIds, animation: true });
            } else {
                showError('未找到匹配的节点');
            }
        }

        function clearSearch() {
            document.getElementById('search-input').value = '';
            network.unselectAll();
            network.fit();
        }

        function filterRelationships() {
            const checkboxes = document.querySelectorAll('#relationship-filters input[type="checkbox"]');
            const selectedTypes = [];
            
            checkboxes.forEach(checkbox => {
                if (checkbox.checked) {
                    const type = checkbox.id.replace('filter-', '');
                    selectedTypes.push(type);
                }
            });

            const filteredEdges = allEdges.filter(edge => selectedTypes.includes(edge.label));
            edges.clear();
            edges.add(filteredEdges);
        }

        function selectAllRelationships() {
            const checkboxes = document.querySelectorAll('#relationship-filters input[type="checkbox"]');
            checkboxes.forEach(checkbox => checkbox.checked = true);
            filterRelationships();
        }

        function deselectAllRelationships() {
            const checkboxes = document.querySelectorAll('#relationship-filters input[type="checkbox"]');
            checkboxes.forEach(checkbox => checkbox.checked = false);
            filterRelationships();
        }

        function fitNetwork() {
            network.fit({ animation: true });
        }

        function resetLayout() {
            network.setData({ nodes: nodes, edges: edges });
            network.fit();
        }

        function togglePhysics() {
            physicsEnabled = !physicsEnabled;
            network.setOptions({ physics: { enabled: physicsEnabled } });
        }

        function exportNetwork() {
            // 简单的导出功能
            alert('导出功能需要额外的库支持，这里仅作演示');
        }

        function highlightConnectedNodes(nodeId) {
            const connectedNodes = network.getConnectedNodes(nodeId);
            const connectedEdges = network.getConnectedEdges(nodeId);
            
            network.selectNodes([nodeId, ...connectedNodes]);
            network.selectEdges(connectedEdges);
        }

        function showError(message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message';
            errorDiv.textContent = message;
            
            const sidebar = document.querySelector('.sidebar');
            sidebar.insertBefore(errorDiv, sidebar.firstChild);
            
            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        }
    </script>
</body>
</html>
