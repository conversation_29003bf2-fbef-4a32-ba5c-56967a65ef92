================================================================================
GraphRAG Parquet文件结构分析报告
================================================================================
分析时间: 2025-06-27 22:58:38
分析文件数量: 7

📊 总览统计
----------------------------------------
总文件大小: 0.16 MB
总记录数: 112

📁 文件: communities.parquet
------------------------------------------------------------
路径: output\communities.parquet
大小: 0.01 MB
行数: 3
列数: 12

📋 列信息:
  • id
    类型: object
    空值: 0 (0.0%)
    唯一值: 3
    样本: 11aa290b-c85c-43d5-9093-5e7285500287, 27510ac0-537b-468a-8bcb-83f105e249d1, ade84b4c-e6c9-44af-961b-235ab4e7eda0

  • human_readable_id
    类型: int64
    空值: 0 (0.0%)
    唯一值: 3
    样本: 0, 1, 2
    统计: min=0, max=2, mean=1.0

  • community
    类型: int64
    空值: 0 (0.0%)
    唯一值: 3
    样本: 0, 1, 2
    统计: min=0, max=2, mean=1.0

  • level
    类型: int64
    空值: 0 (0.0%)
    唯一值: 1
    样本: 0, 0, 0
    统计: min=0, max=0, mean=0.0

  • parent
    类型: int32
    空值: 0 (0.0%)
    唯一值: 1
    样本: -1, -1, -1
    统计: min=-1, max=-1, mean=-1.0

  • children
    类型: object
    空值: 0 (0.0%)
    唯一值: Cannot calculate (complex data type)
    样本: [Array with 0 items], [Array with 0 items], [Array with 0 items]

  • title
    类型: object
    空值: 0 (0.0%)
    唯一值: 3
    样本: Community 0, Community 1, Community 2

  • entity_ids
    类型: object
    空值: 0 (0.0%)
    唯一值: Cannot calculate (complex data type)
    样本: [Array with 7 items], [Array with 4 items], [Array with 2 items]

  • relationship_ids
    类型: object
    空值: 0 (0.0%)
    唯一值: Cannot calculate (complex data type)
    样本: [Array with 14 items], [Array with 6 items], [Array with 1 items]

  • text_unit_ids
    类型: object
    空值: 0 (0.0%)
    唯一值: Cannot calculate (complex data type)
    样本: [Array with 15 items], [Array with 14 items], [Array with 1 items]

  • period
    类型: object
    空值: 0 (0.0%)
    唯一值: 1
    样本: 2025-06-27, 2025-06-27, 2025-06-27

  • size
    类型: int64
    空值: 0 (0.0%)
    唯一值: 3
    样本: 7, 4, 2
    统计: min=2, max=7, mean=4.3333


📁 文件: community_reports.parquet
------------------------------------------------------------
路径: output\community_reports.parquet
大小: 0.05 MB
行数: 3
列数: 15

📋 列信息:
  • id
    类型: object
    空值: 0 (0.0%)
    唯一值: 3
    样本: 28775e6d80934d5fad5c09778f623ebc, 210268a39b514384b3db1a8ce8d74983, d949af32d68349158e2f5a4d2da72ffd

  • human_readable_id
    类型: int32
    空值: 0 (0.0%)
    唯一值: 3
    样本: 0, 1, 2
    统计: min=0, max=2, mean=1.0

  • community
    类型: int32
    空值: 0 (0.0%)
    唯一值: 3
    样本: 0, 1, 2
    统计: min=0, max=2, mean=1.0

  • level
    类型: int64
    空值: 0 (0.0%)
    唯一值: 1
    样本: 0, 0, 0
    统计: min=0, max=0, mean=0.0

  • parent
    类型: int32
    空值: 0 (0.0%)
    唯一值: 1
    样本: -1, -1, -1
    统计: min=-1, max=-1, mean=-1.0

  • children
    类型: object
    空值: 0 (0.0%)
    唯一值: Cannot calculate (complex data type)
    样本: [Array with 0 items], [Array with 0 items], [Array with 0 items]

  • title
    类型: object
    空值: 0 (0.0%)
    唯一值: 3
    样本: Python Function Structure: 函数, 形参, 返回值, 列表, WHILE循, 函数实参类型与传递规则社区, 参数列表与参数列表行结构社区

  • summary
    类型: object
    空值: 0 (0.0%)
    唯一值: 3
    样本: This community centers on the core components of P, 本社区围绕函数调用中的实参类型及其传递规则展开，涵盖了实参、位置实参、关键字实参以及位置实参的顺序等, 本社区围绕“参数列表”及其组成部分“参数列表行”展开，描述了函数定义中参数列表的结构化组织方式。参数

  • full_content
    类型: object
    空值: 0 (0.0%)
    唯一值: 3
    样本: # Python Function Structure: 函数, 形参, 返回值, 列表, WHIL, # 函数实参类型与传递规则社区

本社区围绕函数调用中的实参类型及其传递规则展开，涵盖了实参、位置实, # 参数列表与参数列表行结构社区

本社区围绕“参数列表”及其组成部分“参数列表行”展开，描述了函数

  • rank
    类型: float64
    空值: 0 (0.0%)
    唯一值: 3
    样本: 8.5, 6.5, 2.0
    统计: min=2.0, max=8.5, mean=5.6667

  • rating_explanation
    类型: object
    空值: 0 (0.0%)
    唯一值: 3
    样本: The impact severity rating is high due to the foun, 该社区对编程实践具有较高的重要性，直接影响代码的正确性、可读性和维护性，但其影响范围主要局限于编程和, 该社区的影响程度较低，主要涉及编程结构的基础组成，对更广泛系统或社会层面影响有限。

  • findings
    类型: object
    空值: 0 (0.0%)
    唯一值: Cannot calculate (complex data type)
    样本: [Array with 8 items], [Array with 7 items], [Array with 5 items]

  • full_content_json
    类型: object
    空值: 0 (0.0%)
    唯一值: 3
    样本: {
    "title": "Python Function Structure: 函数, 形参,, {
    "title": "函数实参类型与传递规则社区",
    "summary": "本社, {
    "title": "参数列表与参数列表行结构社区",
    "summary": "本

  • period
    类型: object
    空值: 0 (0.0%)
    唯一值: 1
    样本: 2025-06-27, 2025-06-27, 2025-06-27

  • size
    类型: int64
    空值: 0 (0.0%)
    唯一值: 3
    样本: 7, 4, 2
    统计: min=2, max=7, mean=4.3333


📁 文件: documents.parquet
------------------------------------------------------------
路径: output\documents.parquet
大小: 0.03 MB
行数: 1
列数: 7

📋 列信息:
  • id
    类型: object
    空值: 0 (0.0%)
    唯一值: 1
    样本: 3ebcb132392d910d998ca11f5aebae5ff2817f72c7d4b350c5

  • human_readable_id
    类型: int64
    空值: 0 (0.0%)
    唯一值: 1
    样本: 1
    统计: min=1, max=1, mean=1.0

  • title
    类型: object
    空值: 0 (0.0%)
    唯一值: 1
    样本: 第八章.txt

  • text
    类型: object
    空值: 0 (0.0%)
    唯一值: 1
    样本: ## 第 8 章　函数

在本章中，你将学习编写 函数 （ function ）。函数是带名字的代码

  • text_unit_ids
    类型: object
    空值: 0 (0.0%)
    唯一值: Cannot calculate (complex data type)
    样本: [Array with 16 items]

  • creation_date
    类型: object
    空值: 0 (0.0%)
    唯一值: 1
    样本: 2025-06-27 11:25:56 +0800

  • metadata
    类型: object
    空值: 1 (100.0%)
    唯一值: 0


📁 文件: entities.parquet
------------------------------------------------------------
路径: output\entities.parquet
大小: 0.01 MB
行数: 13
列数: 10

📋 列信息:
  • id
    类型: object
    空值: 0 (0.0%)
    唯一值: 13
    样本: 6b29a49e-1c0f-4178-aa5e-687f8727dd09, 1e827128-6f38-4717-a602-9477dce3ec71, c3c08b0a-4a73-4789-b986-d16bc9297d4f, 79243777-bcf8-498b-b60e-0e6f033ebeb5, e6aa2225-efd3-4926-843c-da04c46e307f

  • human_readable_id
    类型: int64
    空值: 0 (0.0%)
    唯一值: 13
    样本: 0, 1, 2, 3, 4
    统计: min=0, max=12, mean=6.0

  • title
    类型: object
    空值: 0 (0.0%)
    唯一值: 13
    样本: 函数, 形参, 实参, 返回值, 位置实参的顺序

  • type
    类型: object
    空值: 0 (0.0%)
    唯一值: 10
    样本: 函数, 形参, 实参, 返回值, 位置实参的顺序

  • description
    类型: object
    空值: 0 (0.0%)
    唯一值: 12
    样本: 函数是具有特定名称的代码块，用于封装和完成特定的操作或任务。通过定义函数，可以将相关的代码组织在一起, “形参”是指在函数定义时，在括号内声明的变量名，用于接收调用函数时传递进来的实参值。形参决定了函数可, “实参”是指在调用函数时传递给函数的实际值。每当函数被调用时，实参为函数的形参提供具体的数据，使函数, “返回值”是指函数在执行完毕后，通过return语句返回给调用者的数据或结果。返回值的主要作用是将函, “位置实参的顺序”是指在函数调用时，实参（即传入函数的参数）必须按照函数定义中形参（即函数声明时的参

  • text_unit_ids
    类型: object
    空值: 0 (0.0%)
    唯一值: Cannot calculate (complex data type)
    样本: [Array with 15 items], [Array with 14 items], [Array with 14 items], [Array with 12 items], [Array with 14 items]

  • frequency
    类型: int64
    空值: 0 (0.0%)
    唯一值: 7
    样本: 15, 14, 14, 12, 14
    统计: min=1, max=15, mean=9.0769

  • degree
    类型: int32
    空值: 0 (0.0%)
    唯一值: 9
    样本: 11, 8, 7, 4, 4
    统计: min=1, max=11, mean=4.9231

  • x
    类型: int64
    空值: 0 (0.0%)
    唯一值: 1
    样本: 0, 0, 0, 0, 0
    统计: min=0, max=0, mean=0.0

  • y
    类型: int64
    空值: 0 (0.0%)
    唯一值: 1
    样本: 0, 0, 0, 0, 0
    统计: min=0, max=0, mean=0.0


📁 文件: relationships.parquet
------------------------------------------------------------
路径: output\relationships.parquet
大小: 0.01 MB
行数: 38
列数: 8

📋 列信息:
  • id
    类型: object
    空值: 0 (0.0%)
    唯一值: 38
    样本: 549443d5-e81c-47c0-91f7-c50169962df2, 0c94b16c-269b-41e7-b8ab-f86759f77060, 93af8808-735c-4960-88c6-2b4532df1593, 47325597-77f8-41cf-b183-d756385c7e1e, 6b46f2ac-6449-4645-a801-ef41f0e2a958

  • human_readable_id
    类型: int64
    空值: 0 (0.0%)
    唯一值: 38
    样本: 0, 1, 2, 3, 4
    统计: min=0, max=37, mean=18.5

  • source
    类型: object
    空值: 0 (0.0%)
    唯一值: 12
    样本: 函数, 函数, 函数, 函数, 函数

  • target
    类型: object
    空值: 0 (0.0%)
    唯一值: 12
    样本: 形参, 实参, 返回值, 位置实参, 关键字实参

  • description
    类型: object
    空值: 0 (0.0%)
    唯一值: 19
    样本: The entities "函数" (function) and "形参" (formal para, USES, The entities "函数" (function) and "返回值" (return val, USES, USES

  • weight
    类型: float64
    空值: 0 (0.0%)
    唯一值: 22
    样本: 115.0, 100.0, 107.0, 29.0, 29.0
    统计: min=1.0, max=126.0, mean=34.3947

  • combined_degree
    类型: int64
    空值: 0 (0.0%)
    唯一值: 15
    样本: 19, 18, 15, 16, 17
    统计: min=4, max=22, mean=12.4737

  • text_unit_ids
    类型: object
    空值: 0 (0.0%)
    唯一值: Cannot calculate (complex data type)
    样本: [Array with 15 items], [Array with 12 items], [Array with 14 items], [Array with 4 items], [Array with 4 items]


📁 文件: relationships_with_types.parquet
------------------------------------------------------------
路径: output\relationships_with_types.parquet
大小: 0.01 MB
行数: 38
列数: 10

📋 列信息:
  • id
    类型: object
    空值: 0 (0.0%)
    唯一值: 38
    样本: 549443d5-e81c-47c0-91f7-c50169962df2, 0c94b16c-269b-41e7-b8ab-f86759f77060, 93af8808-735c-4960-88c6-2b4532df1593, 47325597-77f8-41cf-b183-d756385c7e1e, 6b46f2ac-6449-4645-a801-ef41f0e2a958

  • human_readable_id
    类型: int64
    空值: 0 (0.0%)
    唯一值: 38
    样本: 0, 1, 2, 3, 4
    统计: min=0, max=37, mean=18.5

  • source
    类型: object
    空值: 0 (0.0%)
    唯一值: 12
    样本: 函数, 函数, 函数, 函数, 函数

  • target
    类型: object
    空值: 0 (0.0%)
    唯一值: 12
    样本: 形参, 实参, 返回值, 位置实参, 关键字实参

  • description
    类型: object
    空值: 0 (0.0%)
    唯一值: 19
    样本: The entities "函数" (function) and "形参" (formal para, USES, The entities "函数" (function) and "返回值" (return val, USES, USES

  • weight
    类型: float64
    空值: 0 (0.0%)
    唯一值: 22
    样本: 115.0, 100.0, 107.0, 29.0, 29.0
    统计: min=1.0, max=126.0, mean=34.3947

  • combined_degree
    类型: int64
    空值: 0 (0.0%)
    唯一值: 15
    样本: 19, 18, 15, 16, 17
    统计: min=4, max=22, mean=12.4737

  • text_unit_ids
    类型: object
    空值: 0 (0.0%)
    唯一值: Cannot calculate (complex data type)
    样本: [Array with 15 items], [Array with 12 items], [Array with 14 items], [Array with 4 items], [Array with 4 items]

  • relationship_type
    类型: object
    空值: 0 (0.0%)
    唯一值: 13
    样本: CONTAINS, USES, UNKNOWN, USES, USES

  • extracted_relationship_type
    类型: object
    空值: 0 (0.0%)
    唯一值: 13
    样本: CONTAINS, USES, UNKNOWN, USES, USES


📁 文件: text_units.parquet
------------------------------------------------------------
路径: output\text_units.parquet
大小: 0.04 MB
行数: 16
列数: 8

📋 列信息:
  • id
    类型: object
    空值: 0 (0.0%)
    唯一值: 16
    样本: 70b8d4c5fdbf97667f175052efa608b18b98a382ab28f4e436, 3100d8839db4e3b2d8a1f94ac5db6b15ce07baa15c74d5fcb6, 0a3f33c8880700596a56b57571128fb6ad4868ca4a5a4d9374, 7205ab4a20bce3374940fe2032338f419d24562e3c198fe863, e448618d00a37a2b9317f692b258bb0db42beb6aa87443ac8d

  • human_readable_id
    类型: int64
    空值: 0 (0.0%)
    唯一值: 16
    样本: 1, 2, 3, 4, 5
    统计: min=1, max=16, mean=8.5

  • text
    类型: object
    空值: 0 (0.0%)
    唯一值: 16
    样本: ## 第 8 章　函数

在本章中，你将学习编写 函数 （ function ）。函数是带名字的代码, 供这种信息（人名）时，它将打印相应 的问候语。

形参

在 greet\_user() 函数的定义, 名为 Willie 的 小狗的信息。至此，有-只名为 Harry 的仓鼠，还有-条名为 Willie, pet() 来描述小狗时，就可以不提供该信息：

```
print(f"My {animal_ty, 缺少必要的信息，并用 traceback 指出了这- 点：

```
Traceback (most

  • n_tokens
    类型: int64
    空值: 0 (0.0%)
    唯一值: 2
    样本: 1200, 1200, 1200, 1200, 1200
    统计: min=366, max=1200, mean=1147.875

  • document_ids
    类型: object
    空值: 0 (0.0%)
    唯一值: Cannot calculate (complex data type)
    样本: [Array with 1 items], [Array with 1 items], [Array with 1 items], [Array with 1 items], [Array with 1 items]

  • entity_ids
    类型: object
    空值: 1 (6.25%)
    唯一值: Cannot calculate (complex data type)
    样本: [Array with 8 items], [Array with 7 items], [Array with 6 items], [Array with 8 items], [Array with 8 items]

  • relationship_ids
    类型: object
    空值: 1 (6.25%)
    唯一值: Cannot calculate (complex data type)
    样本: [Array with 10 items], [Array with 10 items], [Array with 15 items], [Array with 13 items], [Array with 12 items]

  • covariate_ids
    类型: object
    空值: 0 (0.0%)
    唯一值: Cannot calculate (complex data type)
    样本: [Array with 0 items], [Array with 0 items], [Array with 0 items], [Array with 0 items], [Array with 0 items]

