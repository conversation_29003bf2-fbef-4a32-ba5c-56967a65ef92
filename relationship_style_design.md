# 关系类型样式设计文档

## 1. 设计原则

### 1.1 色彩心理学原理

基于色彩心理学和信息可视化最佳实践，为不同类型的关系设计了独特的视觉表示：

- **蓝色系**: 表示层次和结构关系（IS_A, PART_OF）
- **绿色系**: 表示包含和组织关系（CONTAINS, BELONGS_TO）
- **橙色系**: 表示功能和操作关系（TEACHES, USES）
- **红色系**: 表示因果和对立关系（CAUSES, OPPOSES）
- **紫色系**: 表示协作和属性关系（COLLABORATES_WITH, HAS_PROPERTY）
- **灰色系**: 表示一般性和未知关系（RELATED_TO, UNKNOWN）

### 1.2 视觉区分策略

1. **颜色差异**: 每种关系类型使用独特的颜色
2. **箭头类型**: 不同的箭头形状表示不同的关系性质
3. **线条样式**: 实线、虚线、点线表示关系的确定性
4. **线条粗细**: 反映关系的重要性和强度

## 2. 关系类型分类

### 2.1 层次结构类

#### IS_A (蓝色实线箭头)
- **颜色**: #2B7CE9 (深蓝色)
- **箭头**: 标准箭头，放大1.2倍
- **线条**: 实线，宽度2
- **语义**: 表示"是一种"的分类关系

#### PART_OF (绿色粗线箭头)
- **颜色**: #4AD63A (绿色)
- **箭头**: 标准箭头，放大1.3倍
- **线条**: 实线，宽度3
- **语义**: 表示"部分-整体"关系

#### BELONGS_TO (粉色虚线箭头)
- **颜色**: #FF69B4 (粉色)
- **箭头**: 标准箭头，放大1.1倍
- **线条**: 虚线[10,5]，宽度2
- **语义**: 表示归属关系

### 2.2 功能操作类

#### USES (青色条形箭头)
- **颜色**: #00CED1 (青色)
- **箭头**: 条形箭头，放大1.1倍
- **线条**: 实线，宽度2
- **语义**: 表示使用关系

#### TEACHES (橙色实线箭头)
- **颜色**: #FFA500 (橙色)
- **箭头**: 标准箭头，放大1.2倍
- **线条**: 实线，宽度2
- **语义**: 表示教学关系

#### EXTENDS (青绿色虚线箭头)
- **颜色**: #20B2AA (青绿色)
- **箭头**: 标准箭头，放大1.1倍
- **线条**: 虚线[8,4]，宽度2
- **语义**: 表示扩展关系

### 2.3 因果关系类

#### CAUSES (红色粗线箭头)
- **颜色**: #FA0A10 (红色)
- **箭头**: 标准箭头，放大1.4倍
- **线条**: 实线，宽度3
- **语义**: 表示直接因果关系

#### LEADS_TO (橙红色虚线箭头)
- **颜色**: #FF6347 (橙红色)
- **箭头**: 标准箭头，放大1.2倍
- **线条**: 虚线[7,3]，宽度2
- **语义**: 表示导致关系

### 2.4 社交协作类

#### COLLABORATES_WITH (紫色圆形箭头)
- **颜色**: #9A4DFF (紫色)
- **箭头**: 圆形箭头，放大1.2倍
- **线条**: 实线，宽度2
- **语义**: 表示协作关系

#### OPPOSES (红色虚线箭头)
- **颜色**: #FA0A10 (红色)
- **箭头**: 标准箭头，放大1.2倍
- **线条**: 虚线[4,4]，宽度2
- **语义**: 表示对立关系

### 2.5 属性描述类

#### HAS_PROPERTY (紫色圆形箭头)
- **颜色**: #9370DB (紫色)
- **箭头**: 圆形箭头，标准大小
- **线条**: 实线，宽度2
- **语义**: 表示属性关系

#### CONTAINS (绿色方形箭头)
- **颜色**: #32CD32 (绿色)
- **箭头**: 方形箭头，放大1.2倍
- **线条**: 实线，宽度2
- **语义**: 表示包含关系

### 2.6 引用提及类

#### MENTIONS (灰色细虚线箭头)
- **颜色**: #848484 (灰色)
- **箭头**: 标准箭头，缩小0.8倍
- **线条**: 虚线[5,5]，宽度1
- **语义**: 表示提及关系

#### CITES (深灰色条形箭头)
- **颜色**: #708090 (深灰色)
- **箭头**: 条形箭头，标准大小
- **线条**: 虚线[3,3]，宽度1
- **语义**: 表示引用关系

### 2.7 时间关系类

#### OCCURS_BEFORE (蓝色长虚线箭头)
- **颜色**: #4169E1 (皇家蓝)
- **箭头**: 标准箭头，放大1.1倍
- **线条**: 虚线[12,4]，宽度2
- **语义**: 表示时间先后关系

#### PREREQUISITE (橙红色粗线箭头)
- **颜色**: #FF4500 (橙红色)
- **箭头**: 标准箭头，放大1.3倍
- **线条**: 实线，宽度3
- **语义**: 表示前置条件关系

### 2.8 一般关系类

#### RELATED_TO (灰色细虚线箭头)
- **颜色**: #808080 (灰色)
- **箭头**: 标准箭头，缩小0.9倍
- **线条**: 虚线[3,3]，宽度1
- **语义**: 表示一般相关关系

#### SIMILAR_TO (淡紫色圆形箭头)
- **颜色**: #DDA0DD (淡紫色)
- **箭头**: 圆形箭头，标准大小
- **线条**: 虚线[5,3]，宽度1
- **语义**: 表示相似关系

#### UNKNOWN (浅灰色细虚线箭头)
- **颜色**: #C0C0C0 (浅灰色)
- **箭头**: 标准箭头，缩小0.8倍
- **线条**: 虚线[2,2]，宽度1
- **语义**: 表示未知关系

## 3. 箭头类型说明

### 3.1 箭头形状含义

- **arrow**: 标准箭头 - 表示明确的方向性关系
- **circle**: 圆形箭头 - 表示属性、协作等非强制性关系
- **bar**: 条形箭头 - 表示使用、引用等工具性关系
- **box**: 方形箭头 - 表示包含、容器等结构性关系

### 3.2 箭头大小含义

- **1.4倍**: 最重要的关系（因果关系）
- **1.3倍**: 重要关系（包含、管理、前置）
- **1.2倍**: 中等重要关系（分类、教学、协作）
- **1.1倍**: 一般关系（使用、扩展、时间）
- **1.0倍**: 标准关系（属性、相似）
- **0.9倍**: 次要关系（一般相关）
- **0.8倍**: 最次要关系（提及、未知）

## 4. 线条样式说明

### 4.1 实线 (dashes: false)
表示确定性高、关系明确的连接：
- 层次关系 (IS_A, PART_OF)
- 功能关系 (USES, TEACHES)
- 因果关系 (CAUSES)
- 包含关系 (CONTAINS)

### 4.2 虚线样式

#### 长虚线 [12,4]
- 用于时间关系 (OCCURS_BEFORE)
- 表示时间跨度较长的关系

#### 中虚线 [10,5], [8,4], [7,3]
- 用于归属关系 (BELONGS_TO)
- 用于扩展关系 (EXTENDS)
- 用于导致关系 (LEADS_TO)

#### 短虚线 [5,5], [5,3], [4,4], [3,3]
- 用于提及关系 (MENTIONS)
- 用于引用关系 (CITES)
- 用于对立关系 (OPPOSES)
- 用于一般关系 (RELATED_TO)

#### 点线 [2,2]
- 用于未知关系 (UNKNOWN)
- 表示不确定性

## 5. 宽度映射

- **宽度3**: 最重要的关系（CAUSES, PART_OF, PREREQUISITE, MANAGES）
- **宽度2**: 重要关系（大部分明确关系）
- **宽度1**: 次要关系（MENTIONS, CITES, RELATED_TO, SIMILAR_TO, UNKNOWN）

## 6. 实现建议

### 6.1 动态样式应用

```javascript
function getEdgeStyle(relationshipType) {
    const styles = relationshipStyles.styles;
    return styles[relationshipType] || styles.UNKNOWN;
}
```

### 6.2 样式优先级

1. 首先检查精确匹配的关系类型
2. 如果没有匹配，使用UNKNOWN样式
3. 支持样式的动态修改和扩展

### 6.3 可访问性考虑

- 确保颜色对比度符合WCAG标准
- 提供颜色盲友好的替代方案
- 支持高对比度模式
